import { <PERSON><PERSON> } from '../../core/bot'
import { Boat } from '../../data/boat'
import { Teleport } from '../../data/teleport'
import { TransportationConnection } from '../../data/transportationConnection'
import { State, createState } from '../core/script/state'
import { StatefulScript } from '../core/script/statefulScript'
import { Withdraw } from '../game/bank'
import { Dialogue } from '../game/dialogue'
import { GameObjects } from '../game/gameObjects'
import { Inventory } from '../game/inventory'
import { Npcs } from '../game/npcs'
import { Varps } from '../game/varps'
import { Walking } from '../game/walking'
import { Widgets } from '../game/widgets'
import { getCache, putCache } from '../utils/cache'
import { Time } from '../utils/time'
import { log } from '../utils/utils'
import { GameObject } from '../wrappers/gameObject'
import { Player } from '../wrappers/player'
import { MenuOpcode } from './menuOpcode'
import { Tile, TileType } from './tile'

export class WebNodeType {
    static REGULAR: WebNodeType = new WebNodeType('REGULAR')
    static OBJECT: WebNodeType = new WebNodeType('OBJECT')
    static STAIRS: WebNodeType = new WebNodeType('STAIRS')
    static TRAPDOOR: WebNodeType = new WebNodeType('TRAPDOOR')
    static BOAT: WebNodeType = new WebNodeType('BOAT')
    static BOAT_LEAVE: WebNodeType = new WebNodeType('BOAT_LEAVE')
    static NPC: WebNodeType = new WebNodeType('NPC')
    static INVENTORY_TELEPORT: WebNodeType = new WebNodeType('INVENTORY_TELEPORT')
    static TRANSPORTATION_CONNECTION: WebNodeType = new WebNodeType('TRANSPORTATION_CONNECTION')

    name: string

    constructor(name: string) {
        this.name = name
    }

    static forName(name: string) {
        switch (name) {
            case 'REGULAR':
                return this.REGULAR
            case 'OBJECT':
                return this.OBJECT
            case 'STAIRS':
                return this.STAIRS
            case 'NPC':
                return this.NPC
            case 'TRAPDOOR':
                return this.TRAPDOOR
            case 'BOAT':
                return this.BOAT
            case 'INVENTORY_TELEPORT':
                return this.INVENTORY_TELEPORT
            case 'TRANSPORTATION_CONNECTION':
                return this.TRANSPORTATION_CONNECTION
        }
    }
}

export class WebNode extends Tile {
    _finalInPath: boolean = false
    destinations: WebNode[] = []
    destinationNodeIds: number[] = []

    id: number
    type: string
    objectName: string
    npcName: string
    dialogues: string[]
    objectId: number
    action: string
    climbUpAction: string
    climbDownAction: string
    teleport: Teleport
    boat: Boat
    transportationConnection: TransportationConnection
    checkRequirements?: () => boolean

    constructor()
    constructor(position: Tile)
    constructor(position: Tile, parent: WebNode)

    constructor(position?: Tile, parent?: WebNode) {
        super(position?.x ?? 0, position?.y ?? 0, position?.z ?? 0)

        if (position) {
            this.id = Math.floor(Math.random() * 150000000)
            this.type = 'REGULAR'
            if (parent) {
                parent.addDestination(this)
                this.addDestination(parent)
            }
        } else {
            this.type = 'REGULAR'
        }

        this.tileType = TileType.WORLD
    }

    setDestinationNodes(nodesMap: Map<number, WebNode>): void {
        this.destinations = this.destinationNodeIds.map((n) => nodesMap.get(n)).filter((n) => n != null)
    }

    removeDestinations(...nodes: WebNode[]): void {
        for (const node of nodes) {
            this.removeDestination(node)
        }
    }

    removeDestination(node: WebNode): void {
        const index = this.destinationNodeIds.indexOf(node.id)
        if (index !== -1) {
            this.destinationNodeIds.splice(index, 1)
            const destinationIndex = this.destinations.indexOf(node)
            if (destinationIndex != -1) {
                this.destinations.splice(destinationIndex, 1)
            }
        }
    }

    connectOneWay(node: WebNode): void {
        this.addDestination(node)
    }

    connectBoth(node: WebNode): void {
        node.addDestination(this)
        this.addDestination(node)
    }

    addDestination(node: WebNode): void {
        if (!this.destinations.includes(node)) {
            this.destinations.push(node)
            this.destinationNodeIds.push(node.id)
        }
    }

    makeFinalInPath(isFinal: boolean): WebNode {
        this._finalInPath = isFinal
        return this
    }

    toString = () => `WebNode [${this.x}, ${this.y}, ${this.z}, ${this.id}, ${this.type}, ${this.teleport != null ? 'teleport:' + this.teleport?.name : ''}}] `

    equals(other: any): boolean {
        return other.id === this.id
    }

    public hasTraversed(nextNode: WebNode | null, areaRadius: number): boolean {
        if (nextNode) {
            return nextNode.distance() <= areaRadius
        }
        return this.distance() <= areaRadius && this.isReachable()
    }

    public traverse(nextNode: Tile | null, randomize: boolean): boolean {
        log(`Traversing ${this}, ${this.type}`)
        switch (this.type) {
            case WebNodeType.OBJECT.name:
                return this.traverseObjectWebNode(nextNode, randomize)
            case WebNodeType.NPC.name:
                return this.traverseNpcWebNode(nextNode, randomize)
            case WebNodeType.STAIRS.name:
                return this.traverseStairsWebNode(nextNode, randomize)
            case WebNodeType.TRAPDOOR.name:
                return this.traverseTrapdoorWebNode(nextNode, randomize)
            case WebNodeType.INVENTORY_TELEPORT.name:
                return this.traverseInventoryTeleport(nextNode, randomize)
            case WebNodeType.BOAT.name:
                return this.traverseBoatWebNode(nextNode, randomize)
            case WebNodeType.TRANSPORTATION_CONNECTION.name:
                return this.traverseTransportationConnectionWebNode(nextNode, randomize)
            default:
                if (this.distance() > 0) Walking.click(this.randomize(2))
                return true
        }
    }
    traverseInventoryTeleport(nextNode: Tile, randomize: boolean): boolean {
        log('Traversing by teleport: ', this)
        if (this.teleport.destination.distance() <= 10) {
            log('Already traversed')
            return true
        }
        this.teleport.process()
        log('teleport: ', this)
        return true
    }

    createWithdrawItems(defaultState: State, requiredItems?: Withdraw[]) {
        return createState('WebWalking - Withdraw required items (withdraw_boat)', () => {
            for (const item of requiredItems) {
                if (!Withdraw.process(item)) {
                    return
                }
            }

            const statefulScript = Bot.scriptHandler.currentScript as StatefulScript
            if (!statefulScript) return
            statefulScript.currentState = defaultState
        })
    }

    traverseBoatWebNode(nextNode: Tile | null, randomize: boolean): boolean {
        const boat = this.boat

        if (!boat) return false

        if (boat.requiredItems && !boat.requiredItems.every((item) => Inventory.get().countByIds(item.itemId) >= item.minimumAmount)) {
            const statefulScript = Bot.scriptHandler.currentScript as StatefulScript
            if (!statefulScript) return


            const defaultState = statefulScript.state
            
            if(defaultState?.name.includes("withdraw_boat")) {
                log("Failsafe. Just tele to lumby instead of using boat because we dont have cash there.")
                Teleport.lumbridgeHomeTeleport.process()
                return false
            }
            
            const state = this.createWithdrawItems(
                defaultState,
                boat.requiredItems.map((item) => Withdraw.id(item.itemId, item.amountToWithdraw, item.minimumAmount).ensureSpace())
            )
            statefulScript.currentState = state
            return false
        }


        if (Widgets.get(boat.widgetId)?.isRendered) {
            Widgets.get(boat.widgetId)?.click(57, 1)
            Time.sleep(1000, 1600)
            return
        }

        if (Dialogue.containsOption("Okay, don't ask again")) {
            Dialogue.goNext("Okay, don't ask again")
            return
        }

        log('Traversing by boat web node: ', boat.locationName)

        const npc = Npcs.get((npc) => npc.id == boat.npcId)
        if (!npc) {
            Walking.walkTo(boat.tile)
            return
        }

        const actionToClick = npc.transformedDefinition.actions.findIndex((a) => a?.toLowerCase().includes(boat.locationName.toLowerCase()))
        log('Npc transformed: ', npc.transformedDefinition.actions)
        if (actionToClick > -1) {
            log('Boat, clicking npc action: ', actionToClick, ' ', npc.transformedDefinition.actions[actionToClick])
            npc.click(9 + actionToClick)
            return
        }

        npc.click(12)
        Time.sleep(1200)

        if (boat.isCharter && Time.sleep(() => Widgets.get(57999364)?.isRendered)) {
            Time.sleep(1200)

            for(let i = 0; i < 35; i++) {
                let teleChild = Widgets.getByRootId(885, 4, i)
                if(teleChild?.text?.toLowerCase().includes(boat.locationName.toLowerCase())) {
                    Widgets.getPackedChild(57999364, i - 1)?.click(57, 1)
                    Time.sleep(3000)
                    return
                }
            }
            
            return
        }

        if (!boat.isCharter && Time.sleep(() => Widgets.get(boat.widgetId)?.isRendered)) {
            Widgets.get(boat.widgetId)?.click(57, 1)
            if (Time.sleep(() => boat.destination.distance(Player.local.tile, true) <= 20)) {
                Time.sleep(3000, 3200)
            }
            return
        }



        return false
    }

    traverseTransportationConnectionWebNode(nextNode: Tile | null, randomize: boolean): boolean {
        const transportationConnection = this.transportationConnection

        if (!transportationConnection) return false

        log('Traversing by transportation connection web node')

          // Handle widget-based transportation connections
        if (transportationConnection.widgetId) {
            if (Widgets.get(transportationConnection.widgetId)?.isRendered) {
                Widgets.get(transportationConnection.widgetId)?.click(57, 1)
                Time.sleep(1000, 1600)
                return true
            }
        }
        
        // Handle object-based transportation connections
        if (transportationConnection.objectId) {
            const gameObject = GameObjects.getNearest(
                this,
                (o) => o.id === transportationConnection.objectId,
                10
            )

            if (!gameObject) {
                Walking.walkTo(transportationConnection.tile)
                return false
            }

            gameObject.click(12) // Default click
            Time.sleep(1200, 1600)
            return true
        }

        // Handle NPC-based transportation connections
        if (transportationConnection.npcId) {
            const npc = Npcs.get((npc) => npc.id === transportationConnection.npcId)
            if (!npc) {
                Walking.walkTo(transportationConnection.tile)
                return false
            }

            npc.click(12) // Default click
            Time.sleep(1200, 1600)
            return true
        }

      

        // Fallback to walking to the tile
        Walking.walkTo(transportationConnection.tile)
        return false
    }

    skipDialogs() {
        if (this?.objectName.includes('Barrier') && Dialogue.isOpen()) {
            Dialogue.goNext("Yes, and don't ask")
            return false
        } //
        if (this?.objectName.includes('Dungeon entrance') && Dialogue.isOpen()) {
            Dialogue.goNext('Yes')
            return false
        }

        if (this.distance(new Tile(2945, 3041, 0)) < 10) {
            if (Dialogue.contains('Sorry to have')) {
                Dialogue.goNext()
                Time.sleep(4000)
                return false
            }

            if (Dialogue.isOpen()) {
                Dialogue.goNext('Glough sent me', 'Ka.', 'Lu.', 'Min.')
                return false
            }
        }

        if(this.distance(new Tile(1389, 2918, 0)) < 5 && Dialogue.isOpen()) {
                Dialogue.goNext()
                return false
        }

        const wildernessDialog = Widgets.get(31129611)

        if (wildernessDialog?.isRendered) {
            const dontAskAgain = Widgets.get(31129613)
            if (dontAskAgain?.isRendered) {
                dontAskAgain.click(57, 1)
                Time.sleep(100, 200)
            }
            wildernessDialog.click(57, 1)
            Time.sleep(600, 1500)
            return false
        }

        return true
    }

    // Specific traverse methods for each node type
    private traverseObjectWebNode(nextNode: Tile | null, randomize: boolean): boolean {
        log('Traverse ObjectWebNode')

        if (!this.skipDialogs()) {
            return false
        }

        if (nextNode == null || nextNode.isReachable() || this.distance(Player.local.tile) > 8) {
            log(`r: ${nextNode?.isReachable()}`)
            log(`next: ${nextNode}`)
            Walking.click(this)
            return true
        } else {
            log(`Need to click: ${this} --  ${this.objectName} | ${this.objectId} | action: ${this.action}`)
            const gameObject = GameObjects.getNearest(
                this,
                (o) =>
                    (o.name?.toLowerCase() == this.objectName?.toLowerCase() || o.realDefinition?.name?.toLowerCase() == this.objectName?.toLowerCase()) &&
                    o.realDefinition.actions.includes(this.action),
                10
            )

            log(`Got object: ${gameObject}`)

            if (gameObject == null) return false

            if (new Tile(2794, 2977).distance() <= 3 && this.objectName.includes('Rocks')) {
                Walking.click(new Tile(2795, 2978))
                Time.sleep(1000, 2000)
            }

            gameObject.clickAction(this.action)

            if (this?.objectName.includes('Ladder')) {
                Time.sleep(() => this.distance() > 10)
                log('Traversed Ladder')
                Walking.lastWalkTime = 0
            } else if (this?.objectName.includes('Barrier')) {
                Time.sleep(2300, 3000)
            } else if (new Tile(2461, 3383).distance() < 10) {
                Time.sleep(3000, 3600)
            } else if (new Tile(2908, 3048).distance() < 20) {
                Time.sleep(10000)
                if (Player.local.isAnimating) {
                    Time.sleep(8000)
                    Time.sleep(6000, () => !Player.local.isAnimating)
                }
            } else {
                Time.sleep(600)
                if (!nextNode.isReachable()) {
                    Time.sleep(() => nextNode.isReachable())
                }
            }
            this.skipDialogs()
        }
    }

    private traverseNpcWebNode(nextNode: Tile | null, randomize: boolean): boolean {
        if (this.npcName.includes('Veos') && (!Varps.greatKourendUnlocked || (getCache('veos_talk_started') && !Dialogue.isOpen()))) {
            putCache('veos_talk_started', true, 60000)
            if (Dialogue.talkTo(Npcs.getById(1063), true)) {
                Dialogue.goNext("That's great")
            }
            return false
        }

        log('Looking for NPC: ' + this.npcName)
        const npc = Npcs.getByNameEquals(this.npcName)

        if (npc != null && Dialogue.isOpen()) {
            log('Going with dialogues', this.dialogues)
            Dialogue.goNext(...this.dialogues)
            return false
        }

        if (npc != null) {
            if (this.action?.length > 0) {
                npc.clickAction(this.action)
            } else {
                npc.click(MenuOpcode.NPC_FIRST_OPTION)
            }
            Time.sleep(1500)
            return false
        } else {
            Walking.click(this)
        }

        return true
    }

    private traverseStairsWebNode(nextNode: Tile | null, randomize: boolean): boolean {
        log('Traverse stairs web node: ', this.objectName, nextNode == null, nextNode?.isReachable(), nextNode)
        if (nextNode == null || nextNode.isReachable()) {
            Walking.click(this)
            return true
        } else {
            let action = nextNode.z > this.z ? this.climbUpAction : this.climbDownAction

            log('climbUpAction ', this.climbUpAction)
            log('climbDownAction ', this.climbDownAction)
            log('Perform stairs action: ', action)
            if (new Tile(2906, 9875, 0).distance() < 15) {
                action = this.climbUpAction
            }

            let gameObject: GameObject = null

            if (new Tile(2987, 3295).distance() < 30 && this.objectName == 'Mysterious ruins') {
                //air altar
                gameObject = GameObjects.getById(34813)
                gameObject.click(3)
                Time.sleep(() => this.distance() > 10)
                Walking.lastWalkTime = 0
                return
            }

            if (new Tile(2826, 2998).distance() < 30) {
                gameObject = GameObjects.getByRealId(23585)
                gameObject.click(3)
                Time.sleep(() => this.distance() > 10)
                Walking.lastWalkTime = 0
                return
            }

            //regular
            // gameObject = GameObjects.getNearest(this, (o) => o.name == this.objectName && o.definition.actions.includes(action))

            gameObject = GameObjects.getNearest(
                this,
                (o) => o.name != null && (o.name.toLowerCase() == this?.objectName?.toLowerCase() || o?.realDefinition?.name?.toLowerCase() == this?.objectName.toLowerCase()),
                10
            )

            if (gameObject == null) {
                log(`Could not find game object: ${this.objectName}`)
                return
            }
            gameObject.clickAction(action)
            Time.sleep(() => this.distance() > 10 || Player.local.tile.z != this.z)
            Walking.lastWalkTime = 0
        }
    }

    private traverseTrapdoorWebNode(nextNode: Tile | null, randomize: boolean): boolean {
        log('Traverse trapdoor web node: ', nextNode == null, nextNode?.isReachable(), nextNode)
        if (nextNode == null || nextNode.isReachable()) {
            Walking.click(this)
            return true
        } else {
            const gameObject = GameObjects.getByNameEquals('Trapdoor')

            if (gameObject == null) {
                return false
            }

            let action = gameObject.definition.actions.find((a) => a == 'Open') != null ? 'Open' : 'Climb-down'

            gameObject.clickAction(action)
            if (action.includes('Climb')) {
                Time.sleep(() => this.distance() > 10)
                log('Traversed TrapdoorWebNode')
                Walking.lastWalkTime = 0
            } else {
                Time.sleep(1000, 1600)
            }
        }
    }
}
