import { GameMessageEvent } from '../../../api/core/script/game-events/eventBus'
import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { TrainTask, Combat } from '../../../api/game/combat'
import { EqItem, Equipment } from '../../../api/game/equipment'
import { GameTab } from '../../../api/game/gameTab'
import { Inventory } from '../../../api/game/inventory'
import { Npcs } from '../../../api/game/npcs'
import { Skill } from '../../../api/game/skill'
import { Varps } from '../../../api/game/varps'
import { Walking } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { AccountData } from '../../../api/model/accountData'
import { Tile } from '../../../api/model/tile'
import { PaintTimer } from '../../../api/utils/paintTimer'
import { Random } from '../../../api/utils/random'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Npc } from '../../../api/wrappers/npc'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { ItemPredicate } from '../../../data/itemPredicates'
import { Locations } from '../../../data/locations'
import { Teleport } from '../../../data/teleport'
import { EquipmentItems } from '../equipmentItem'
import { TrainingSpots } from '../trainingSpot'
import { CombatTrainerGe } from './combatTrainerGe'
import { CombatTrainerBase } from './combatTrainerBase'
import { Quest } from '../../../api/game/quests'
import { WorldHopping } from '../../../api/game/worldHopping'
import { Players } from '../../../api/game/players'
import { Client } from '../../../api/wrappers/client'
import { ObtainAvasState } from '../../../api/script-utils/states/obtainAvasState'
import { GroundItems } from '../../../api/game/groundItems'

export class CombatTrainerGebCrab extends CombatTrainerBase {


    onCreate(): void {
   
    }

    onAction(): void {
        this.setState(this.doBanking)
    }


    doBanking = createState('Banking', () => {  
        if (!this.wearEquipment()) {
            return
        }

        this.setState(this.walkSpot)
    })

    fightCrab = createState('Fighting crabs', () => {
        Walking.setRunAuto()


        if(!Inventory.contains(ItemId.SALMON)) {
            this.setState(this.doBanking)
            return
        }

        if(!this.wearEquipment()) {
            return
        }

        if (Skill.HITPOINTS.getCurrentLevel() <= 10) {
            Inventory.getById(ItemId.SALMON).click(57, 2)
            Time.sleep(900, 1200)
        }
      

        if (!this.isAtCrabSpot() ) {
            this.setState(this.walkSpot)
            return
        }

        if (!this.ensureAttackStyle()) {
            return
        }

        if (Player.local.isAnimating) {
            this.lastAnim.reset()
        }
        //TODO attack crab there
    })


    walkSpot = createState('Walking to spot', () => {
        if (this.isAtCrabSpot()) {
            this.setState(this.fightCrab)
            return
        }

        if (!Walking.walkTo(new Tile(1240, 3042, 0), 3)) {
            return
        }
    })

   


    isAtCrabSpot() {
        return new Tile(1240, 3042, 0).distance() < 15 || new Tile(1353, 3112).distance() < 15 || new Tile(1273, 3166).distance() < 15 
    }

    isCrabPresent() {
        return Npcs.getById(-1) != null // TODO
    }

}
