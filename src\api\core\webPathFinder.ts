import { WebData } from '../../data/webData'
import { Equipment } from '../game/equipment'
import { Inventory } from '../game/inventory'
import { Tile } from '../model/tile'
import { WebNode, WebNodeType } from '../model/webNode'
import { getCache, putCache } from '../utils/cache'
import { log } from '../utils/utils'

export class WebPathFinder {
    private checkReachable: boolean = true

    constructor(checkReachable?: boolean) {
        if (checkReachable != null) {
            this.checkReachable = checkReachable
        }
    }

    findPath(startTile: Tile | null, endTile: Tile | null): (WebNode | null)[] {
        let openList: { node: WebNode; priority: number }[] = []

        let parentNodeMap = new Map<WebNode, WebNode | null>()

        let startNode = this.checkReachable ? WebPathFinder.getClosestReachableNode(startTile) : WebPathFinder.getClosestNode(startTile)

        let goalNode: WebNode | null
        goalNode = WebPathFinder.getClosestNode(endTile)

        if (!startNode) {
            //create a fake web node, which will then contain available teleport destinations
            const teleportDestinationWebNode = new WebNode()
            teleportDestinationWebNode.x = startTile.x
            teleportDestinationWebNode.y = startTile.y
            teleportDestinationWebNode.z = startTile.z
            teleportDestinationWebNode.tileType = startTile.tileType
            startNode = teleportDestinationWebNode
        }

        if (!goalNode) {
            return []
        }

        if (goalNode.distance(startNode) == 0) {
            log('goalNode is same as  startNode', goalNode)
            return [goalNode]
        }

        openList.push({ node: startNode, priority: 0 })
        parentNodeMap.set(startNode, null)

        let distanceMap = new Map<WebNode, number>()
        distanceMap.set(startNode, 0)

        const usableTeleports = WebData.nodes.filter(
            (n) =>
                n.type == WebNodeType.INVENTORY_TELEPORT.name &&
                n.teleport.canUsePredicate() &&
                (Inventory.get().containsByPredicate(n.teleport.itemPredicate) || Equipment.get().containsByPredicate(n.teleport.itemPredicate))
        )

        while (openList.length > 0) {
            openList.sort((a, b) => a.priority - b.priority)
            let current = openList.shift()!.node

            if (current.equals(goalNode)) {
                return this.constructPath(startNode, goalNode, parentNodeMap)
            }

            for (let neighborNode of [...current.destinations, ...usableTeleports]) {
                if (neighborNode.checkRequirements && !neighborNode.checkRequirements()) continue

                let tentativeDistance = distanceMap.get(current)! + this.distanceBetween(current, neighborNode)
                if (!distanceMap.has(neighborNode) || tentativeDistance < distanceMap.get(neighborNode)!) {
                    distanceMap.set(neighborNode, tentativeDistance)
                    let priority = tentativeDistance + this.distanceBetween(neighborNode, goalNode)
                    parentNodeMap.set(neighborNode, current)
                    openList.push({ node: neighborNode, priority: priority })
                }
            }
        }
        return []
    }

    distanceBetween(node1: WebNode, node2: WebNode): number {
        if (node1.type == WebNodeType.INVENTORY_TELEPORT.name) return 20 + node1.teleport.additionalCost
        if (node2.type == WebNodeType.INVENTORY_TELEPORT.name) return 20 + node2.teleport.additionalCost

        if (node1.type == WebNodeType.STAIRS.name) return 1
        if (node1.type == WebNodeType.BOAT.name) return 5
        if (node1.type == WebNodeType.TRANSPORTATION_CONNECTION.name) return 5

        return node1.distance(node2)
    }

    pathCost(path: (WebNode | null)[]): number {
        let totalDistance = 0
        for (let i = 0; i < path.length - 1; i++) {
            totalDistance += this.distanceBetween(path[i], path[i + 1])
        }
        return totalDistance
    }

    private static getClosestReachableNode(tile: Tile | null): WebNode | null {
        const cached = getCache('closest_node_' + tile.x + '_' + tile.y)

        if (cached != null) {
            if (cached == 'not_found') {
                return null
            }
            return cached
        }

        const nodes = WebData.nodes.filter((n) => n.type != WebNodeType.INVENTORY_TELEPORT.name).filter((n) => n.distance(tile) < 30)

        nodes.sort((a, b) => a.distance(tile) - b.distance(tile))

        const closest = nodes.find((n) => n.isReachable(tile))

        putCache('closest_node_' + tile.x + '_' + tile.y, closest ?? 'not_found', 3000)
        return closest
    }

    private static getClosestReachableEndNode(tile: Tile | null): WebNode | null {
        let closest: WebNode | null = null
        let lastDistance = Infinity
        for (const node of WebData.nodes) {
            if (node.type == WebNodeType.INVENTORY_TELEPORT.name) continue

            const distance = node.distance(tile)
            if (distance < 30 && distance < lastDistance && node.isReachable(tile)) {
                closest = node
                lastDistance = distance
            }
        }
        return closest
    }

    public static getClosestNode(tile: Tile | null): WebNode | null {
        let closest: WebNode | null = null
        let lastDistance = Infinity
        for (const node of WebData.nodes) {
            if (node.type == WebNodeType.INVENTORY_TELEPORT.name) continue

            const distance = node.distance(tile)
            if (distance < lastDistance) {
                closest = node
                lastDistance = distance
            }
        }
        return closest
    }

    public static getClosestNodeOfType(tile: Tile, type: WebNodeType): WebNode | null {
        let closest: WebNode | null = null
        let lastDistance = Infinity
        for (const node of WebData.nodes) {
            if (node.type != type.name) continue

            const distance = node.distance(tile)
            if (distance < lastDistance) {
                closest = node
                lastDistance = distance
            }
        }
        return closest
    }

    private constructPath(start: WebNode | null, node: WebNode | null, parentNodes: Map<WebNode, WebNode | null>): (WebNode | null)[] {
        const path: (WebNode | null)[] = []
        while (node && parentNodes.has(node)) {
            path.unshift(node)
            node = parentNodes.get(node) || null
        }
        // path.unshift(start)
        return path
    }
}
