import { Tile } from '../api/model/tile'


export class TransportationConnection {
    objectId?: number
    npcId?: number
    widgetId?: number

    tile: Tile
    destination: Tile
    requirements?: () => boolean
}


export const TransportationSystemNodes: Record<string, TransportationConnection> = {
    // Example transportation connection - replace with actual coordinates when ready
    BirdFortisToTalTeklan: {
        npcId: 13355,
        tile: new Tile(1697, 3140, 0),
        destination: new Tile(1226, 3091, 0)
    }
}