import { GameMessageEvent } from '../../../api/core/script/game-events/eventBus'
import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { TrainTask } from '../../../api/game/combat'
import { EqItem, Equipment } from '../../../api/game/equipment'
import { GameTab } from '../../../api/game/gameTab'
import { Inventory } from '../../../api/game/inventory'
import { Npcs } from '../../../api/game/npcs'
import { Quest } from '../../../api/game/quests'
import { Skill } from '../../../api/game/skill'
import { Varps } from '../../../api/game/varps'
import { Walking } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { AccountData } from '../../../api/model/accountData'
import { Tile } from '../../../api/model/tile'
import { PaintTimer } from '../../../api/utils/paintTimer'
import { Random } from '../../../api/utils/random'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Npc } from '../../../api/wrappers/npc'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { ItemPredicate } from '../../../data/itemPredicates'
import { Locations } from '../../../data/locations'
import { Teleport } from '../../../data/teleport'
import { EquipmentItems } from '../equipmentItem'
import { TrainingSpots } from '../trainingSpot'
import { CombatTrainerAmmoniteCrabs } from './combatTrainerAmmoniteCrabs'
import { CombatTrainerBase } from './combatTrainerBase'
import { CombatTrainerGe } from './combatTrainerGe'
import { CombatTrainerGebCrab } from './combatTrainerGemCrab'

export class CombatTrainerMain extends CombatTrainerBase {
    lastAnim: PaintTimer = new PaintTimer()

    npc: Npc
    static currentTask: string = ''
    aggroTimer: PaintTimer = new PaintTimer(Date.now() - 60000 * 9)

    constructor() {
        super()
    }

    resetAggro = createState('Reset aggro', () => {
        if (Walking.walkTo(new Tile(2700, 3682, 0), 1)) {
            Time.sleep(3000)
            this.aggroTimer.reset()
            this.setDefaultState()
        }
    })

    bankAll = createState('Banking items', () => {
        if (Bank.openNearest() && Bank.depositAll()) {
            this.setDefaultState()
            return
        }
    })

    onCreate(): void {
        this.subscribe(GameMessageEvent, (e) => {
            if (e.message.includes('enough free inventory space to do that.')) {
                this.setState(this.bankAll)
            }
        })
    }

    onAction(): void {
        Walking.setRunAuto()

        if (this.useRanged && Skill.RANGE.getCurrentLevel() >= 20 && Quest.CHILDREN_OF_THE_SUN.isCompleted) {
            const strategy = new CombatTrainerGebCrab()
            strategy.useRanged = this.useRanged
            strategy.skillToTrain = this.skillToTrain
            this.setState(strategy, true, false, true)
            return null
        }

        if (!this.useRanged && Skill.STRENGTH.getCurrentLevel() >= 20 && Quest.CHILDREN_OF_THE_SUN.isCompleted) {
            const strategy = new CombatTrainerGebCrab()
            strategy.useRanged = this.useRanged
            strategy.skillToTrain = this.skillToTrain
            this.setState(strategy, true, false, true)
            return null
        }

        if (Skill.HITPOINTS.getPercent() <= Random.nextSeed(AccountData.seed() + new Date().getMinutes(), 60, 75) && Inventory.contains(ItemId.SALMON)) {
            Inventory.getById(ItemId.SALMON).click(57, 2)
            Time.sleep(900, 1200)
        }

        if (!this.wearEquipment()) {
            return
        }

        if (!this.withdrawSupplies()) {
            return
        }

        const spot = this.getSpot()
        if (!Walking.walkTo(spot.tile, spot.area)) {
            return
        }

        if (!spot.tile.isReachable()) {
            Walking.walkTo(spot.tile, 1)
            return
        }

        if (!this.ensureAttackStyle()) {
            return
        }

        if (Player.local.isAnimating) {
            this.lastAnim.reset()
        }

        if (Player.local.interactingIndex > -1 && this.lastAnim.getElapsed() < 4000) {
            return
        }

        if (this.getSpot() == TrainingSpots.rockCrabs1) {
            this.attackCrabs()
        } else {
            this.attackRegularNpcs()
        }
    }
    attackCrabs() {
        const spot = this.getSpot()
        const deathAnims = [1314]
        if (this.npc == null || this.npc.currentHitpoints == 0 || (this.npc.interactingIndex != -1 && this.npc.interactingIndex != Player.local.index) || !this.npc.exists()) {
            this.npc = Npcs.get(
                (n) =>
                    n.tile.distance(spot.tile) <= spot.area &&
                    !deathAnims.includes(n.animation) &&
                    (n.currentHitpoints > 0 || n.currentHitpoints == -1) &&
                    n.definition.name.toLowerCase().includes('rock crab') &&
                    (n.interactingIndex == Player.local.index || Player.local.interactingIndex == n.index)
            )

            if (this.npc == null) {
                this.npc = Npcs.get(
                    (n) =>
                        n.tile.distance(spot.tile) <= spot.area &&
                        !deathAnims.includes(n.animation) &&
                        (n.currentHitpoints > 0 || n.currentHitpoints == -1) &&
                        n.definition.name.toLowerCase().includes('rock crab') &&
                        (n.interactingIndex == -1 || n.interactingIndex == Player.local.index)
                )
            }

            if (this.npc == null) {
                const n = Npcs.get((n) => n.tile.distance(spot.tile) <= spot.area && n.definition.name.includes('Rocks'))
                if (n != null) {
                    Walking.walkTo(n.tile, 0)
                    Time.sleep(1200, 1600)
                }

                if (n.tile.distance() <= 1 && this.npc == null && this.aggroTimer.getElapsedSeconds() > 9 * 60) {
                    this.setState(this.resetAggro)
                    return
                }
                return
            }
        }

        if (this.npc != null) {
            this.npc.click(10)
            Time.sleep(1000, 1500)
            this.lastAnim.reset()
        }
    }

    attackRegularNpcs() {
        const spot = this.getSpot()

        if (this.npc == null || [5051, 836, 1795].includes(this.npc.animation) || (this.npc.interactingIndex != -1 && this.npc.interactingIndex != Player.local.index) || !this.npc.exists()) {
            this.npc = Npcs.get(
                (n) =>
                    n.index != this.npc?.index &&
                    ![5051, 836, 1795].includes(n.animation) &&
                    n.tile.distance(spot.tile) <= spot.area &&
                    spot.npcNames.includes(n.definition.name) &&
                    n.tile.isReachable() &&
                    n.interactingIndex == -1
            )
        }

        if (this.npc != null) {
            this.npc.click(10)
            Time.sleep(2500, 3500)
            this.lastAnim.reset()
        }
    }

    getSpot() {
        if (Varps.membershipDays > 0) {
            return TrainingSpots.rockCrabs1
        }

        if (Skill.STRENGTH.getCurrentLevel() >= 20 && Skill.ATTACK.getCurrentLevel() >= 20) {
            return TrainingSpots.giantFrogs
        }

        if (Skill.STRENGTH.getCurrentLevel() >= 10 && Skill.ATTACK.getCurrentLevel() >= 10) {
            return TrainingSpots.cows
        }

        return TrainingSpots.chickens
    }

    getSpotRanged() {
        if (Varps.membershipDays > 0 /*&& Skill.RANGE.getCurrentLevel() >= 10*/) {
            return TrainingSpots.rockCrabs1
        }

        if (Skill.RANGE.getCurrentLevel() >= 20) {
            return TrainingSpots.giantFrogs
        }

        if (Skill.RANGE.getCurrentLevel() >= 5) {
            return TrainingSpots.cows
        }

        return TrainingSpots.chickens
    }

    withdrawSupplies() {
        try {
            if (
                !Withdraw.builder()
                    .id(ItemId.SALMON)
                    .amount(15)
                    .minimumAmount(1)
                    .ensureSpace()
                    .orState(() => {
                        const state = new CombatTrainerGe(this)
                        state.skillToTrain = this.skillToTrain
                        return state
                    })
                    .withdraw()
            )
                return false

            if (Varps.membershipDays > 0) {
                if (
                    !Withdraw.builder()
                        .predicate(Teleport.ringOfWealthPredicate)
                        .amount(1)
                        .minimumAmount(1)
                        .ensureSpace()
                        .orState(() => {
                            const state = new CombatTrainerGe(this)
                            state.skillToTrain = this.skillToTrain
                            return state
                        })
                        .withdraw()
                )
                    return false
                if (
                    !Withdraw.builder()
                        .id(ItemId.LUMBRIDGE_TELEPORT)
                        .amount(5)
                        .minimumAmount(1)
                        .ensureSpace()
                        .orState(() => {
                            const state = new CombatTrainerGe(this)
                            state.skillToTrain = this.skillToTrain
                            return state
                        })
                        .withdraw()
                )
                    return false
                if (
                    !Withdraw.builder()
                        .id(ItemId.CAMELOT_TELEPORT)
                        .amount(5)
                        .minimumAmount(1)
                        .ensureSpace()
                        .orState(() => {
                            const state = new CombatTrainerGe(this)
                            state.skillToTrain = this.skillToTrain
                            return state
                        })
                        .withdraw()
                )
                    return false
            }

            if (Locations.getClosestBankStraight().distance() < 20) {
                if (Varps.membershipDays > 0) {
                    if (
                        !Withdraw.builder()
                            .predicate(ItemPredicate.staminaPotion)
                            .amount(1)
                            .minimumAmount(1)
                            .ensureSpace()
                            .orState(() => {
                                const state = new CombatTrainerGe(this)
                                state.skillToTrain = this.skillToTrain
                                return state
                            })
                            .withdraw()
                    )
                        return false
                }
            }
        } catch (e: unknown) {
            log('Bank item not found!')
            if (typeof e === 'object' && e !== null && 'name' in e && e.name == 'BankItemNotFoundException') {
                log(e, (e as { stack?: string }).stack)
                const state = new CombatTrainerGe(this)
                state.skillToTrain = this.skillToTrain
                this.setState(state)
            }
            return false
        }
        return true
    }
}
